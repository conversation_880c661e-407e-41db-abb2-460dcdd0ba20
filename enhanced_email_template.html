  <!doctype html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>DSF Sense — Turn Shoppers Into Voices</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');
    
    /* Preheader text hide */
    .preheader { display:none !important; visibility:hidden; opacity:0; color:transparent; height:0; width:0; overflow:hidden; mso-hide:all; }
    
    /* Animations */
    @keyframes fadeInUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    
    @keyframes gradient {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }
    
    /* Main styles */
    .animated { animation: fadeInUp 0.6s ease-out; }
    .pulse-btn { animation: pulse 2s ease-in-out infinite; }
    
    .gradient-bg {
  background: linear-gradient(135deg, #10B981 0%, #**********%);
  background-size: 100% 100%;
  /* animation: gradient 8s ease infinite; */
    }
    
    .brand-gradient {
      background: linear-gradient(135deg, #10B981 0%, #**********%);
    }
    
    .brand-text-gradient {
      background: linear-gradient(135deg, #10B981 0%, #**********%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .brand-border-gradient {
      background: linear-gradient(135deg, #10B981 0%, #**********%) border-box;
      border: 2px solid transparent;
      background-clip: padding-box, border-box;
    }
    
    .glass-effect {
      background: rgba(255, 255, 255, 0.25);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.18);
    }
    
    .neon-border {
      border: 2px solid transparent;
      background: linear-gradient(135deg, #10B981 0%, #**********%) border-box;
      border-radius: 12px;
      background-clip: padding-box, border-box;
    }
    
    .hover-lift {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .hover-lift:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
    }
    
    .text-gradient {
      background: linear-gradient(135deg, #10B981 0%, #**********%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .stats-counter {
      font-size: 32px;
      font-family: 'Poppins','Inter',Helvetica,sans-serif;
      font-weight: 700;
      color: #10B981;
      text-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
    }
    
    /* Responsive */
    @media screen and (max-width: 600px) {
      .container { width:100% !important; padding-left:0 !important; padding-right:0 !important; }
      .px { padding-left:0 !important; padding-right:0 !important; }
      .stack { display:block !important; width:100% !important; }
      .stats-counter { font-size: 24px; }
      /* Feature grid cards responsive fix */
      .feature-grid-row {
        display: block !important;
      }
      .feature-grid-card {
        width: 100% !important;
        display: block !important;
        margin-bottom: 16px !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
      .feature-grid-card > div {
        display: flex !important;
        align-items: center !important;
        height: auto !important;
        margin: 0 auto !important;
      }
      .feature-grid-card > div > div:first-child {
        font-size: 32px !important;
        margin-right: 15px !important;
        margin-bottom: 0 !important;
        min-width: 40px !important;
        text-align: center !important;
      }
      .feature-grid-card h4, .feature-grid-card p {
        margin-left: 0 !important;
        margin-right: 0 !important;
        word-break: break-word;
        overflow-wrap: break-word;
      }
      .feature-grid-card h4 {
        font-size: 16px !important;
        margin-bottom: 8px !important;
      }
      .feature-grid-card p {
        font-size: 14px !important;
        line-height: 1.4 !important;
      }
    }
  </style>
</head>
<body style="margin:0; padding:0; background: transparent; min-height: 100vh;">
  <!-- Preheader (preview) text -->
  <div class="preheader">
    🚀 Transform your retail insights in 30 minutes! 80% of sales happen in-store—are you listening? Free pilot available.
  </div>

  <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="min-height: 100vh;">
    <tr>
      <td align="center" style="padding:40px 20px;">
        
        <!-- Main container with glass effect -->
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="600" class="container glass-effect animated" 
               style="width:600px; max-width:600px; border-radius:20px; overflow:hidden; box-shadow:0 25px 50px rgba(0,0,0,0.25);">
          
          <!-- Dynamic header with gradient -->
          <tr>
            <td align="center" style="padding:0; background: linear-gradient(135deg, #10B981 0%, #**********%); position: relative;">
              <div style="padding:0; color: white; text-align: center; position: relative; height: 260px; display: table; width: 100%;">
                <div style="position: absolute; top: 0; left: 0; right: 0; height: 2px; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);"></div>
                <div style="display: table-cell; vertical-align: middle;">
                  <img src="https://i.postimg.cc/KYzSwyBV/Group-7.png" alt="DSF Solutions"
                       width="180"
                       style="display:block; max-width:100%; height:auto; margin: 0 auto 20px; opacity: 0.95;">
                  <h1 style="margin:0 0 16px; font-family:'Inter',Arial,sans-serif; font-size:28px; line-height:1.2; font-weight:700; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    🎯 Turn Every Shopper Into<br>
                    <span style="font-size: 32px; background: linear-gradient(45deg, #ffffff, #f0f9ff); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Growth Intelligence</span>
                  </h1>
                  <p style="margin:0; font-family:'Inter',Arial,sans-serif; font-size:16px; opacity:0.9; max-width: 400px; margin: 0 auto;">
                    Capture in-store insights in seconds, not surveys
                  </p>
                </div>
              </div>
            </td>
          </tr>

          <!-- Eye-catching stats section -->
          <tr>
            <td style="padding:0; background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);">
              <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                <tr>
                  <td align="center" style="padding:30px 20px;">
                    <div style="display: flex; justify-content: space-around; align-items: center; text-align: center; max-width: 400px; margin: 0 auto;">
                      <div style="flex: 1;">
                        <div class="stats-counter">80%</div>
                        <div style="font-family:'Poppins','Inter',Helvetica,sans-serif; font-size:12px; color:#64748b; font-weight:500;">SALES IN-STORE</div>
                      </div>
                      <div style="flex: 1;">
                        <div class="stats-counter">10 min</div>
                        <div style="font-family:'Poppins','Inter',Helvetica,sans-serif; font-size:12px; color:#64748b; font-weight:500;">TO SET UP</div>
                      </div>
                      <div style="flex: 1;">
                        <div class="stats-counter">0€</div>
                        <div style="font-family:'Poppins','Inter',Helvetica,sans-serif; font-size:12px; color:#64748b; font-weight:500;">PILOT COST</div>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <!-- Personal greeting -->
          <tr>
            <td class="px" style="padding:20px 40px 0; font-family:'Poppins','Inter',Helvetica,sans-serif; background: #ffffff;">
              <div style="background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); padding: 20px; border-radius: 12px; border-left: 4px solid #10B981;">
                <p style="margin:0 0 12px; font-size:16px; color:#1e293b;">👋 Hi <strong>[First Name]</strong>,</p>
                <p style="margin:0; font-size:15px; line-height:1.6; color:#475569;">
                  Here's a reality check: while <a href="https://www.ey.com/pt_br/insights/retail/should-retailers-close-stores-or-make-them-work-harder" style="color:#10B981; text-decoration: none;"><strong style="color:#10B981;">80% of sales happen in-store</strong></a>, 
                  most feedback strategies are stuck online. You're missing the voices that matter most.
                </p>
              </div>
            </td>
          </tr>

          <!-- Problem/Solution with visual elements -->
          <tr>
            <td class="px" style="padding:30px 40px 20px; font-family:'Poppins','Inter',Helvetica,sans-serif; background: #ffffff;">
              
              <!-- Problem block -->
              <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); padding: 20px; border-radius: 12px; margin: 0 0 20px; border: 1px solid #fca5a5;">
                <div style="display: flex; align-items: flex-start;">
                  <div style="background: #dc2626; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-weight: 700; margin-right: 15px; flex-shrink: 0;">!</div>
                  <div>
                    <h3 style="margin: 0 0 8px; color: #991b1b; font-size: 16px; font-weight: 600;">The Problem</h3>
                    <p style="margin: 0; color: #7f1d1d; font-size: 14px; line-height: 1.5;">Traditional surveys are too long, online feedback misses in-store experiences, and you're flying blind on what customers really think.</p>
                  </div>
                </div>
              </div>

              <!-- Solution block -->
              <div style="background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%); padding: 20px; border-radius: 12px; border: 1px solid #86efac;">
                <div style="display: flex; align-items: flex-start;">
                  <div style="background: #10B981; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-weight: 700; margin-right: 15px; flex-shrink: 0;">✓</div>
                  <div>
                    <h3 style="margin: 0 0 8px; color: #14532d; font-size: 16px; font-weight: 600;">DSF Sense Solution</h3>
                    <p style="margin: 0; color: #166534; font-size: 14px; line-height: 1.5;">Strategic tablet placement captures instant feedback in seconds—no complex surveys, no IT headaches, just pure customer insights powered by AI.</p>
                  </div>
                </div>
              </div>
            </td>
          </tr>

          <!-- Interactive features grid -->
          <tr>
            <td class="px" style="padding:20px 40px 30px; font-family:'Poppins','Inter',Helvetica,sans-serif; background: #ffffff;">
              <h3 style="margin: 0 0 20px; text-align: center; color: #1e293b; font-size: 20px; font-weight: 600;">
                Why retail teams choose DSF Sense
              </h3>
              
              <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                <tr class="feature-grid-row">
                  <td width="50%" class="feature-grid-card" style="padding-right: 10px; vertical-align: top;">
                    <div class="hover-lift" style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); padding: 20px; border-radius: 12px; height: 120px; border: 1px solid #93c5fd; cursor: pointer;">
                      <div style="font-size: 24px; margin-bottom: 8px;">🏪</div>
                      <div>
                        <h4 style="margin: 0 0 8px; color: #1e40af; font-size: 14px; font-weight: 600;">In-Store First</h4>
                        <p style="margin: 0; color: #1e40af; font-size: 12px; line-height: 1.4;">Built for physical retail, not online surveys forced into stores</p>
                      </div>
                    </div>
                  </td>
                  <td width="50%" class="feature-grid-card" style="padding-left: 10px; vertical-align: top;">
                    <div class="hover-lift" style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 20px; border-radius: 12px; height: 120px; border: 1px solid #86efac; cursor: pointer;">
                      <div style="font-size: 24px; margin-bottom: 8px;">⚡</div>
                      <div>
                        <h4 style="margin: 0 0 8px; color: #15803d; font-size: 14px; font-weight: 600;">Lightning Setup</h4>
                        <p style="margin: 0; color: #15803d; font-size: 12px; line-height: 1.4;">From questions to responses in under 30 minutes</p>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="feature-grid-card" style="padding-top: 15px;">
                    <div class="hover-lift" style="background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%); padding: 20px; border-radius: 12px; border: 1px solid #fbbf24; cursor: pointer;">
                      <div style="display: flex; align-items: center;">
                        <div style="font-size: 32px; margin-right: 15px;">🤖</div>
                        <div>
                          <h4 style="margin: 0 0 8px; color: #92400e; font-size: 16px; font-weight: 600;">AI-Powered Insights</h4>
                          <p style="margin: 0; color: #92400e; font-size: 14px; line-height: 1.4;">
                            Surface patterns like <em>"50% of customers mention long checkout times"</em> automatically
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <!-- Compelling CTA section -->
          <tr>
            <td class="px" style="padding:30px 40px; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
              
              <!-- Offer highlight -->
              <div style="background: linear-gradient(135deg, #10B981 0%, #**********%); padding: 2px; border-radius: 16px; margin: 0 0 25px;">
                <div style="background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%); padding: 25px; border-radius: 14px; text-align: center; position: relative;">
                  <div style="position: absolute; top: 10px; right: 15px; background: linear-gradient(135deg, #10B981 0%, #**********%); color: white; padding: 4px 12px; border-radius: 20px; font-size: 11px; font-weight: 600; font-family:'Poppins','Inter',Helvetica,sans-serif; text-transform: uppercase;">Limited Time</div>
                  <div style="font-size: 32px; margin-bottom: 10px;">🎁</div>
                  <h3 style="margin: 0 0 10px; font-family:'Poppins','Inter',Helvetica,sans-serif; font-size: 20px; font-weight: 700; background: linear-gradient(135deg, #10B981 0%, #**********%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">FREE 2-Week Pilot</h3>
                  <p style="margin: 0; font-size: 15px; font-family:'Poppins','Inter',Helvetica,sans-serif; color: #64748b;">Bring your tablet, we handle everything else</p>
                  <div style="margin-top: 15px; padding: 12px; background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%); border-radius: 8px; border: 1px solid #86efac;">
                    <p style="margin: 0; font-size: 13px; font-family:'Poppins','Inter',Helvetica,sans-serif; color: #15803d; font-weight: 500;">✨ Setup, software, and analytics included</p>
                  </div>
                </div>
              </div>

              <!-- CTA Button -->
              <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 0 0 25px;">
                <tr>
                  <td align="center">
                    <table role="presentation" cellpadding="0" cellspacing="0" border="0" class="pulse-btn hover-lift">
                      <tr>
                        <td align="center" style="background: linear-gradient(135deg, #10B981 0%, #**********%); border-radius: 12px; box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);">
                          <a href="[Demo Link]" target="_blank"
                             style="display: inline-block; padding: 16px 32px; font-family:'Poppins','Inter',Helvetica,sans-serif; font-size: 16px; line-height: 1; text-decoration: none; color: #ffffff; font-weight: 700; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                            🚀 Book Your 30-Min Demo
                          </a>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>

              <div style="text-align: center; background: white; padding: 20px; border-radius: 12px; border: 2px dashed #10B981;">
                <p style="margin: 0 0 15px; font-family:'Poppins','Inter',Helvetica,sans-serif; font-size: 16px; color: #1e293b;">
                  Ready to hear from the <strong>80% of customers</strong> you're currently missing?
                </p>
                <p style="margin: 0; font-family:'Poppins','Inter',Helvetica,sans-serif; font-size: 14px; color: #64748b;">
                  Let's show <strong>[Company Name]</strong> how to turn foot traffic into growth insights 📈
                </p>
              </div>
            </td>
          </tr>

          <!-- Personal signature -->
          <tr>
            <td class="px" style="padding: 30px 40px; background: #ffffff; border-top: 1px solid #e2e8f0;">
              <div style="display: flex; align-items: center; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); padding: 20px; border-radius: 12px;">
                <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #10B981 0%, #**********%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: 20px; margin-right: 20px; flex-shrink: 0;">
                  [I]
                </div>
                <div>
                  <p style="margin: 0; font-family:'Poppins','Inter',Helvetica,sans-serif; color: #1e293b; font-size: 15px; line-height: 1.6;">
                    <strong>[Your Name]</strong><br>
                    <span style="color: #10B981;">[Your Title]</span>, DSF Solutions<br>
                    <span style="color: #64748b;">[Your Phone]</span> · 
                    <a href="[Your Website]" style="color: #10B981; text-decoration: none; font-weight: 500;">[Your Website]</a>
                  </p>
                </div>
              </div>
            </td>
          </tr>

        </table>

        <!-- Footer with enhanced styling -->
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="600" class="container" style="width:600px; max-width:600px; margin-top:20px;">
          <tr>
            <td align="center" class="px" style="padding: 20px 40px; font-family:'Poppins','Inter',Helvetica,sans-serif; color: #1e293b; font-size: 12px; line-height: 1.6; background: rgba(255,255,255,0.1); border-radius: 12px; backdrop-filter: blur(10px);">
              <p style="margin: 0 0 10px;">
                Can't see the button? Copy this link: 
                <a href="[Demo Link]" style="color: #00f2fe; text-decoration: none; font-weight: 500;">[Demo Link]</a>
              </p>
              <p style="margin: 0; font-size: 11px; opacity: 0.7;">
                © [Year] DSF Solutions · [Company Address] · 
                <a href="[Unsubscribe Link]" style="color: #1e293b; text-decoration: underline;">Unsubscribe</a>
              </p>
            </td>
          </tr>
        </table>

      </td>
    </tr>
  </table>
</body>
</html>